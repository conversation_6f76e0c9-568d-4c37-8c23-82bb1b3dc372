import React, { useState } from 'react';
import {
  Box,
  Typography,
  Input,
  Button,
  Card,
  CardContent,
  Stack,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemContent,
  ListItemDecorator,
} from '@mui/joy';
import {
  Search,
  FilterList,
  History,
  TrendingUp,
  Article,
  Image,
  VideoLibrary,
} from '@mui/icons-material';

const SearchPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const searchFilters = [
    { id: 'all', label: '全部', icon: <Search /> },
    { id: 'articles', label: '文章', icon: <Article /> },
    { id: 'images', label: '图片', icon: <Image /> },
    { id: 'videos', label: '视频', icon: <VideoLibrary /> },
  ];

  const popularSearches = [
    'AI技术趋势',
    '机器学习算法',
    '深度学习框架',
    '自然语言处理',
    '计算机视觉',
  ];

  const recentSearches = [
    'React最佳实践',
    'TypeScript高级技巧',
    'Web性能优化',
  ];

  const mockResults = [
    {
      id: 1,
      title: 'AI在现代应用中的革命性作用',
      snippet: '人工智能正在改变我们与技术互动的方式，从自动化到个性化体验...',
      source: 'tech-blog.com',
      date: '2024-01-15',
      type: 'article',
    },
    {
      id: 2,
      title: '机器学习模型部署最佳实践',
      snippet: '了解如何将训练好的机器学习模型有效地部署到生产环境中...',
      source: 'ml-insights.org',
      date: '2024-01-12',
      type: 'article',
    },
    {
      id: 3,
      title: 'Joy UI组件库完整指南',
      snippet: 'Joy UI提供了一套现代化的React组件，帮助开发者快速构建美观的用户界面...',
      source: 'ui-guides.net',
      date: '2024-01-10',
      type: 'guide',
    },
  ];

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // TODO: 实现实际的搜索逻辑
    console.log('搜索:', query);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 0 }}>
      {/* 搜索标题 */}
      <Typography level="h2" sx={{ mb: 3, fontWeight: 'bold' }}>
        智能搜索
      </Typography>

      {/* 搜索栏 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Stack spacing={3}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Input
                placeholder="搜索任何内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                startDecorator={<Search />}
                endDecorator={
                  <IconButton size="sm" variant="soft">
                    <FilterList />
                  </IconButton>
                }
                sx={{ flex: 1 }}
                size="lg"
              />
              <Button
                variant="solid"
                size="lg"
                onClick={() => handleSearch(searchQuery)}
              >
                搜索
              </Button>
            </Box>

            {/* 搜索过滤器 */}
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {searchFilters.map((filter) => (
                <Chip
                  key={filter.id}
                  variant={selectedFilter === filter.id ? 'solid' : 'outlined'}
                  color={selectedFilter === filter.id ? 'primary' : 'neutral'}
                  startDecorator={filter.icon}
                  onClick={() => setSelectedFilter(filter.id)}
                  sx={{ cursor: 'pointer' }}
                >
                  {filter.label}
                </Chip>
              ))}
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '2fr 1fr' }, gap: 3 }}>
        {/* 搜索结果 */}
        <Box>
          {searchQuery ? (
            <>
              <Typography level="body-md" sx={{ mb: 2, color: 'text.secondary' }}>
                找到 {mockResults.length} 个结果（用时 0.32 秒）
              </Typography>
              <Stack spacing={2}>
                {mockResults.map((result) => (
                  <Card key={result.id} variant="outlined" sx={{ cursor: 'pointer', '&:hover': { boxShadow: 'md' } }}>
                    <CardContent>
                      <Stack spacing={1}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Chip size="sm" variant="soft" color="primary">
                            {result.type}
                          </Chip>
                          <Typography level="body-xs" color="neutral">
                            {result.source} • {result.date}
                          </Typography>
                        </Box>
                        <Typography level="title-md" color="primary" sx={{ cursor: 'pointer' }}>
                          {result.title}
                        </Typography>
                        <Typography level="body-sm" color="neutral">
                          {result.snippet}
                        </Typography>
                      </Stack>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </>
          ) : (
            <Card>
              <CardContent>
                <Typography level="body-md" textAlign="center" color="neutral">
                  输入关键词开始搜索
                </Typography>
              </CardContent>
            </Card>
          )}
        </Box>

        {/* 侧边栏 */}
        <Box>
          <Stack spacing={3}>
            {/* 热门搜索 */}
            <Card>
              <CardContent>
                <Typography level="title-md" startDecorator={<TrendingUp />} sx={{ mb: 2 }}>
                  热门搜索
                </Typography>
                <List size="sm">
                  {popularSearches.map((search, index) => (
                    <ListItem key={index}>
                      <ListItemContent>
                        <Typography
                          level="body-sm"
                          sx={{ cursor: 'pointer', '&:hover': { color: 'primary.500' } }}
                          onClick={() => handleSearch(search)}
                        >
                          {search}
                        </Typography>
                      </ListItemContent>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>

            {/* 最近搜索 */}
            <Card>
              <CardContent>
                <Typography level="title-md" startDecorator={<History />} sx={{ mb: 2 }}>
                  最近搜索
                </Typography>
                <List size="sm">
                  {recentSearches.map((search, index) => (
                    <ListItem key={index}>
                      <ListItemDecorator>
                        <History fontSize="small" />
                      </ListItemDecorator>
                      <ListItemContent>
                        <Typography
                          level="body-sm"
                          sx={{ cursor: 'pointer', '&:hover': { color: 'primary.500' } }}
                          onClick={() => handleSearch(search)}
                        >
                          {search}
                        </Typography>
                      </ListItemContent>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>

            {/* 搜索提示 */}
            <Card>
              <CardContent>
                <Typography level="title-md" sx={{ mb: 2 }}>
                  搜索技巧
                </Typography>
                <List size="sm">
                  <ListItem>
                    <ListItemContent>
                      <Typography level="body-sm">
                        使用引号搜索精确短语
                      </Typography>
                    </ListItemContent>
                  </ListItem>
                  <ListItem>
                    <ListItemContent>
                      <Typography level="body-sm">
                        使用 + 号包含必需词汇
                      </Typography>
                    </ListItemContent>
                  </ListItem>
                  <ListItem>
                    <ListItemContent>
                      <Typography level="body-sm">
                        使用 - 号排除特定词汇
                      </Typography>
                    </ListItemContent>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
};

export default SearchPage; 