import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssVarsProvider, extendTheme } from '@mui/joy/styles';
import CssBaseline from '@mui/joy/CssBaseline';
import GlobalStyles from '@mui/joy/GlobalStyles';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import SearchPage from './pages/SearchPage';
import AppLayout from './components/Layout/AppLayout';
import { useAuthStore } from './utils/authStore';

// 扩展主题配置
const customTheme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        background: {
          surface: '#ffffff',
          level1: '#f8f9fa',
          level2: '#f1f3f4',
        },
      },
    },
    dark: {
      palette: {
        background: {
          surface: '#0b0d0f',
          level1: '#131619',
          level2: '#1a1d21',
        },
      },
    },
  },
  fontFamily: {
    body: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    display: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    JoySheet: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
        },
      },
    },
  },
});

// 主应用组件
const MainApp: React.FC = () => {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <AppLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/search" element={<SearchPage />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </AppLayout>
  );
};

function App() {
  return (
    <CssVarsProvider theme={customTheme} defaultMode="system">
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Transition-duration': '0.2s', // 缩短过渡时间
            '--joy-fontFamily-body': '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          },
          '*': {
            boxSizing: 'border-box',
          },
          'html, body': {
            margin: 0,
            padding: 0,
            minHeight: '100vh',
            // 添加防闪烁样式
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
          },
          // 防止布局抖动
          '#root': {
            isolation: 'isolate',
          },
        }}
      />
      <Router>
        <MainApp />
      </Router>
    </CssVarsProvider>
  );
}

export default App;
