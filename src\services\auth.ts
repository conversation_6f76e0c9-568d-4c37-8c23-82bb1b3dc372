import { encryptPassword } from '../utils/crypto';
import type { User } from '../utils/authStore';
import { post, get, put } from './api';

// 登录请求接口
interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应接口（根据openapi.json定义）
interface LoginResponse {
  token: string;
  token_type: string;
  uid: number;
  username: string;
  name: string;
  tenant_id: number;
}

// 登出响应接口
interface LogoutResponse {
  message: string;
}

// 用户个人信息响应接口
interface ProfileResponse {
  uid: number;
  username: string;
  name: string;
  tenant_id: number;
  gender: number;
}

// 密码更新请求接口
interface PasswordUpdateRequest {
  old_password: string;
  new_password: string;
}

/**
 * 用户登录
 * @param username 用户名
 * @param password 密码
 * @returns 用户信息
 */
export const login = async (username: string, password: string): Promise<User> => {
  // 使用SHA256加密密码
  const encryptedPassword = encryptPassword(password);

  const data: LoginRequest = {
    username,
    password: encryptedPassword,
  };

  const response = await post<LoginResponse>('/auth/login', data);

  return {
    uid: response.uid,
    username: response.username,
    name: response.name,
    tenant_id: response.tenant_id,
    token: response.token,
  };
};

/**
 * 用户登出
 * @param token 用户token（此参数在当前API设计中不需要，因为拦截器会自动添加）
 */
export const logout = async (): Promise<void> => {
  await post<LogoutResponse>('/auth/logout');
};

/**
 * 获取用户个人信息
 * @returns 用户个人信息
 */
export const getProfile = async (): Promise<ProfileResponse> => {
  return get<ProfileResponse>('/auth/profile');
};

/**
 * 更新密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 */
export const updatePassword = async (
  oldPassword: string,
  newPassword: string
): Promise<void> => {
  const encryptedOldPassword = encryptPassword(oldPassword);
  const encryptedNewPassword = encryptPassword(newPassword);

  const data: PasswordUpdateRequest = {
    old_password: encryptedOldPassword,
    new_password: encryptedNewPassword,
  };

  await put<{ message: string }>('/auth/password', data);
}; 