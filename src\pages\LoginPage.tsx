import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CssVarsProvider,
  extendTheme,
  useColorScheme,
} from '@mui/joy/styles';
import GlobalStyles from '@mui/joy/GlobalStyles';
import CssBaseline from '@mui/joy/CssBaseline';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  IconButton,
  Input,
  Stack,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/joy';
import type { IconButtonProps } from '@mui/joy';
import {
  DarkModeRounded,
  LightModeRounded,
  Visibility,
  VisibilityOff,
  Person,
  Lock,
} from '@mui/icons-material';
import { login } from '../services/auth';
import { useAuthStore } from '../utils/authStore';

interface LoginFormData {
  username: string;
  password: string;
}

interface FormElements extends HTMLFormControlsCollection {
  username: HTMLInputElement;
  password: HTMLInputElement;
}

interface LoginFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

// 主题切换组件
function ColorSchemeToggle(props: IconButtonProps) {
  const { onClick, ...other } = props;
  const { mode, setMode } = useColorScheme();
  const [mounted, setMounted] = React.useState(false);
  
  React.useEffect(() => setMounted(true), []);
  
  if (!mounted) {
    return <IconButton size="sm" variant="outlined" color="neutral" />;
  }
  
  return (
    <IconButton
      id="toggle-mode"
      size="sm"
      variant="outlined"
      color="neutral"
      aria-label="toggle light/dark mode"
      {...other}
      onClick={(event) => {
        setMode(mode === 'light' ? 'dark' : 'light');
        onClick?.(event);
      }}
    >
      {mode === 'light' ? <DarkModeRounded /> : <LightModeRounded />}
    </IconButton>
  );
}

// 自定义主题
const customTheme = extendTheme({});

const LoginPage: React.FC = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const { login: authLogin, isAuthenticated } = useAuthStore();
  const navigate = useNavigate();

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleInputChange = (field: keyof LoginFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // 清除错误信息
    if (error) setError('');
  };

  const handleSubmit = async (event: React.FormEvent<LoginFormElement>) => {
    event.preventDefault();
    
    const formElements = event.currentTarget.elements;
    const data = {
      username: formElements.username.value,
      password: formElements.password.value,
    };
    
    if (!data.username.trim()) {
      setError('请输入用户名');
      return;
    }
    
    if (!data.password.trim()) {
      setError('请输入密码');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const user = await login(data.username, data.password);
      authLogin(user);
      
      // 登录成功后导航到首页
      navigate('/');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : '登录失败，请检查用户名和密码';
      setError(errorMessage);
      // 清空密码字段
      setFormData(prev => ({ ...prev, password: '' }));
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <CssVarsProvider theme={customTheme} defaultMode="dark">
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Transition-duration': '0.4s',
          },
        }}
      />
      
      <Box
        sx={(theme) => ({
          width: { xs: '100%', md: '50vw' },
          transition: 'width var(--Transition-duration)',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'flex-end',
          backdropFilter: 'blur(12px)',
          backgroundColor: 'rgba(255 255 255 / 0.2)',
          [theme.getColorSchemeSelector('dark')]: {
            backgroundColor: 'rgba(19 19 24 / 0.4)',
          },
        })}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100dvh',
            width: '100%',
            px: 2,
          }}
        >
          <Box
            component="header"
            sx={{
              py: 3,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            {/* Logo */}
            <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
              <Box
                component="img"
                src="/logo.png"
                alt="锤磨AI Logo"
                sx={{
                  width: 32,
                  height: 32,
                }}
              />
              <Typography level="title-lg">锤磨AI</Typography>
            </Box>
            <ColorSchemeToggle />
          </Box>
          
          <Box
            component="main"
            sx={{
              my: 'auto',
              py: 2,
              pb: 5,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              width: 400,
              maxWidth: '100%',
              mx: 'auto',
              borderRadius: 'sm',
              '& form': {
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              },
            }}
          >
            <Stack gap={4} sx={{ mb: 2 }}>
              <Stack gap={1}>
                <Typography component="h1" level="h3">
                  登录
                </Typography>
              </Stack>
            </Stack>
            
            {/* 错误提示 */}
            {error && (
              <Alert color="danger" sx={{ mb: 1 }}>
                {error}
              </Alert>
            )}
            
            {/* 登录表单 */}
            <Stack gap={4} sx={{ mt: 2 }}>
              <form onSubmit={handleSubmit}>
                <FormControl required>
                  <FormLabel>用户名</FormLabel>
                  <Input
                    type="text"
                    name="username"
                    placeholder="请输入用户名"
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    startDecorator={<Person />}
                    disabled={loading}
                    autoFocus
                  />
                </FormControl>
                
                <FormControl required>
                  <FormLabel>密码</FormLabel>
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    placeholder="请输入密码"
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    startDecorator={<Lock />}
                                       endDecorator={
                     <IconButton
                       variant="plain"
                       color="neutral"
                       onClick={togglePasswordVisibility}
                       onMouseDown={(e) => e.preventDefault()}
                       size="sm"
                       sx={{
                         userSelect: 'none',
                         '&:focus': {
                           outline: 'none',
                         },
                       }}
                     >
                       {showPassword ? <VisibilityOff /> : <Visibility />}
                     </IconButton>
                   }
                    disabled={loading}
                  />
                </FormControl>
                
                                 <Stack gap={4} sx={{ mt: 2 }}>
                   <Button
                    type="submit"
                    fullWidth
                    loading={loading}
                    loadingIndicator={<CircularProgress size="sm" />}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Stack>
              </form>
            </Stack>
          </Box>
          
          <Box component="footer" sx={{ py: 3 }}>
            <Typography level="body-xs" textAlign="center">
              © 锤磨AI {new Date().getFullYear()}
            </Typography>
          </Box>
        </Box>
      </Box>
      
      {/* 右侧背景图片 */}
      <Box
        sx={(theme) => ({
          height: '100%',
          position: 'fixed',
          right: 0,
          top: 0,
          bottom: 0,
          left: { xs: 0, md: '50vw' },
          transition:
            'background-image var(--Transition-duration), left var(--Transition-duration) !important',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          backgroundColor: 'background.level1',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundImage:
            'url(https://images.unsplash.com/photo-1527181152855-fc03fc7949c8?auto=format&w=1000&dpr=2)',
          [theme.getColorSchemeSelector('dark')]: {
            backgroundImage:
              'url(https://images.unsplash.com/photo-1572072393749-3ca9c8ea0831?auto=format&w=1000&dpr=2)',
          },
        })}
      />
    </CssVarsProvider>
  );
};

export default LoginPage; 