import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stack,
  Chip,
  Avatar,
  Grid,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemContent,
  ListItemDecorator,
  Divider,
} from '@mui/joy';
import {
  Person,
  TrendingUp,
  Analytics,
  Schedule,
  Star,
  Search,
  History,
  Storage,
} from '@mui/icons-material';
import { useAuthStore } from '../utils/authStore';

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();

  if (!user) {
    return null;
  }

  const stats = [
    { label: '今日搜索', value: '127', trend: '+12%', color: 'primary' as const },
    { label: '收藏项目', value: '48', trend: '+5%', color: 'success' as const },
    { label: '历史记录', value: '1,234', trend: '+8%', color: 'warning' as const },
    { label: '活跃时间', value: '4.2h', trend: '+15%', color: 'danger' as const },
  ];

  const recentActivities = [
    { action: '搜索了 "AI技术趋势"', time: '5分钟前', icon: <Search /> },
    { action: '收藏了文章 "机器学习最佳实践"', time: '1小时前', icon: <Star /> },
    { action: '查看了历史记录', time: '2小时前', icon: <History /> },
    { action: '创建了新收藏夹 "前端开发"', time: '1天前', icon: <Storage /> },
  ];

  const quickActions = [
    { label: '智能搜索', description: '开始新的搜索', icon: <Search />, color: 'primary' as const },
    { label: '我的收藏', description: '查看收藏的内容', icon: <Star />, color: 'warning' as const },
    { label: '分析报告', description: '查看使用统计', icon: <Analytics />, color: 'success' as const },
    { label: '历史记录', description: '浏览搜索历史', icon: <History />, color: 'neutral' as const },
  ];

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 0 }}>
      {/* 欢迎区域 */}
      <Box sx={{ mb: 4 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Box>
            <Typography level="h2" sx={{ fontWeight: 'bold' }}>
              欢迎回来，{user.name}！
            </Typography>
            <Typography level="body-lg" color="neutral">
              今天是一个探索知识的好日子
            </Typography>
          </Box>
          <Avatar size="lg">
            <Person />
          </Avatar>
        </Stack>
        
        <Card>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box sx={{ flex: 1 }}>
                <Typography level="body-md" sx={{ mb: 1 }}>
                  今日进度
                </Typography>
                <LinearProgress
                  determinate
                  value={68}
                  sx={{ mb: 1 }}
                  color="primary"
                />
                <Typography level="body-sm" color="neutral">
                  您今天已完成 68% 的学习目标
                </Typography>
              </Box>
              <Chip variant="soft" color="success" size="lg">
                表现出色
              </Chip>
            </Stack>
          </CardContent>
        </Card>
      </Box>

      {/* 统计卡片 */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid key={index} xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Stack spacing={1}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography level="body-sm" color="neutral">
                      {stat.label}
                    </Typography>
                    <TrendingUp fontSize="small" color="success" />
                  </Box>
                  <Typography level="h3" fontWeight="bold">
                    {stat.value}
                  </Typography>
                  <Chip variant="soft" color={stat.color} size="sm">
                    {stat.trend}
                  </Chip>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* 快速操作 */}
        <Grid xs={12} md={6}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography level="title-lg" sx={{ mb: 2 }}>
                快速操作
              </Typography>
              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid key={index} xs={6}>
                    <Card
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          boxShadow: 'md',
                          transform: 'translateY(-2px)',
                        },
                        transition: 'all 0.2s ease',
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', py: 3 }}>
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: '50%',
                            backgroundColor: `${action.color}.softBg`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 2,
                            color: `${action.color}.500`,
                          }}
                        >
                          {action.icon}
                        </Box>
                        <Typography level="title-sm" sx={{ mb: 0.5 }}>
                          {action.label}
                        </Typography>
                        <Typography level="body-xs" color="neutral">
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* 最近活动 */}
        <Grid xs={12} md={6}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography level="title-lg" sx={{ mb: 2 }}>
                最近活动
              </Typography>
              <List size="sm">
                {recentActivities.map((activity, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemDecorator>
                        <Box
                          sx={{
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            backgroundColor: 'primary.softBg',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'primary.500',
                          }}
                        >
                          {activity.icon}
                        </Box>
                      </ListItemDecorator>
                      <ListItemContent>
                        <Typography level="body-sm" sx={{ mb: 0.5 }}>
                          {activity.action}
                        </Typography>
                        <Typography level="body-xs" color="neutral">
                          {activity.time}
                        </Typography>
                      </ListItemContent>
                    </ListItem>
                    {index < recentActivities.length - 1 && <Divider inset="context" />}
                  </React.Fragment>
                ))}
              </List>
              <Button variant="plain" sx={{ mt: 2, width: '100%' }}>
                查看所有活动
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 推荐内容 */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography level="title-lg" sx={{ mb: 3 }}>
            为您推荐
          </Typography>
          <Grid container spacing={2}>
            {[
              {
                title: 'React 18 新特性详解',
                description: '深入了解 React 18 的并发特性和自动批处理',
                category: '前端开发',
                readTime: '8分钟阅读',
              },
              {
                title: 'TypeScript 高级技巧',
                description: '掌握 TypeScript 的高级类型和工具类型',
                category: '编程语言',
                readTime: '12分钟阅读',
              },
              {
                title: 'Web 性能优化策略',
                description: '从加载时间到用户体验的全方位优化',
                category: '性能优化',
                readTime: '15分钟阅读',
              },
            ].map((item, index) => (
              <Grid key={index} xs={12} sm={4}>
                <Card variant="outlined" sx={{ cursor: 'pointer', '&:hover': { boxShadow: 'md' } }}>
                  <CardContent>
                    <Stack spacing={1}>
                      <Chip variant="soft" size="sm" color="primary">
                        {item.category}
                      </Chip>
                      <Typography level="title-sm">
                        {item.title}
                      </Typography>
                      <Typography level="body-sm" color="neutral">
                        {item.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                        <Schedule fontSize="small" />
                        <Typography level="body-xs" color="neutral">
                          {item.readTime}
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard; 