import React, { useState, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Sheet,
  List,
  ListItem,
  ListItemButton,
  ListItemDecorator,
  ListItemContent,
  Typography,
  IconButton,
  Avatar,
  Dropdown,
  Menu,
  MenuButton,
  MenuItem,
  Divider,
  Chip,
  Stack,
  useColorScheme,
} from '@mui/joy';
import {
  KeyboardArrowLeft,
  Person,
  ExitToApp,
  School,
  Settings,
  KeyboardArrowRight,
  LightMode,
  DarkMode,
} from '@mui/icons-material';
import { useAuthStore } from '../../utils/authStore';
import { logout } from '../../services/auth';

interface AppLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactElement;
  path?: string;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    id: 'learning-center',
    label: '学习中心',
    icon: <School />,
    path: '/learning-center',
  },
];

// 状态持久化到localStorage
const SIDEBAR_STORAGE_KEY = 'app-sidebar-state';

const getSavedSidebarState = (): boolean => {
  try {
    const saved = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    return saved ? JSON.parse(saved) : true;
  } catch {
    return true;
  }
};

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(getSavedSidebarState);
  const [showThemeMenu, setShowThemeMenu] = useState(false);
  const { user, logout: authLogout } = useAuthStore();
  const { mode, setMode } = useColorScheme();
  const navigate = useNavigate();
  const location = useLocation();

  // 缓存侧边栏切换函数
  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prev => {
      const newState = !prev;
      localStorage.setItem(SIDEBAR_STORAGE_KEY, JSON.stringify(newState));
      return newState;
    });
  }, []);

  // 缓存登出处理函数
  const handleLogout = useCallback(async () => {
    try {
      await logout();
    } catch (error) {
      console.error('登出错误:', error);
    } finally {
      authLogout();
    }
  }, [authLogout]);

  // 缓存主题切换函数
  const toggleColorMode = useCallback((newMode: 'light' | 'dark') => {
    setMode(newMode);
    setShowThemeMenu(false); // 选择后关闭菜单
  }, [setMode]);

  // 缓存导航处理函数
  const handleNavigation = useCallback((path: string) => {
    if (location.pathname !== path) {
      navigate(path);
    }
  }, [navigate, location.pathname]);

  // 优化渲染导航项的函数
  const renderNavItem = useCallback((item: NavItem, depth = 0) => {
    const isActive = location.pathname === item.path;

    return (
      <React.Fragment key={item.id}>
        <ListItem sx={{ pl: depth * 2 }}>
          <ListItemButton
            onClick={() => {
              if (item.path) {
                handleNavigation(item.path);
              }
            }}
            sx={{
              borderRadius: 'md',
              backgroundColor: isActive ? 'primary.softBg' : 'transparent',
              color: isActive ? 'primary.softColor' : 'inherit',
              transition: 'all 0.15s ease-in-out',
              py: 1.5,
              pl: sidebarOpen ? 2 : 1.5, // 调整左边距以对齐logo
              '&:hover': {
                backgroundColor: isActive ? 'primary.softBg' : 'background.level2',
                transform: 'none',
              },
              // 移除选中状态的边框
              '&:focus-visible': {
                outline: 'none',
                backgroundColor: isActive ? 'primary.softBg' : 'background.level2',
              },
            }}
          >
            <ListItemDecorator sx={{
              minWidth: sidebarOpen ? 32 : 24, // 与logo宽度对齐
              fontSize: '1.5rem',
              display: 'flex',
              justifyContent: sidebarOpen ? 'flex-start' : 'center',
            }}>
              {item.icon}
            </ListItemDecorator>
            {sidebarOpen && (
              <>
                <ListItemContent sx={{ ml: 1.5 }}>
                  <Typography level="body-md" fontWeight="md" sx={{ fontSize: '1rem' }}>
                    {item.label}
                  </Typography>
                </ListItemContent>
              </>
            )}
          </ListItemButton>
        </ListItem>
      </React.Fragment>
    );
  }, [location.pathname, sidebarOpen, handleNavigation]);

  // 计算侧边栏宽度的样式
  const sidebarWidth = useMemo(() => sidebarOpen ? 280 : 64, [sidebarOpen]);
  
  // 缓存侧边栏样式
  const sidebarStyles = useMemo(() => ({
    width: sidebarWidth,
    transition: 'width 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    borderRight: '1px solid',
    borderColor: 'divider',
    display: 'flex',
    flexDirection: 'column' as const,
    position: 'fixed' as const,
    height: '100vh',
    left: 0,
    top: 0,
    zIndex: 1000,
    backgroundColor: 'background.surface',
  }), [sidebarWidth]);

  // 缓存主内容区域样式
  const mainContentStyles = useMemo(() => ({
    flex: 1,
    marginLeft: `${sidebarWidth}px`,
    transition: 'margin-left 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    display: 'flex',
    flexDirection: 'column' as const,
    minHeight: '100vh',
  }), [sidebarWidth]);

  // 渲染用户菜单内容
  const renderUserMenuContent = useCallback(() => {
    if (showThemeMenu) {
      return (
        <>
          {/* 顶部用户姓名 */}
          <Box sx={{ px: 3, py: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
            <Typography level="body-md" fontWeight="lg" sx={{ fontSize: '1rem' }}>
              {user?.name}
            </Typography>
            <Typography level="body-xs" sx={{ color: 'text.tertiary', mt: 0.5 }}>
              主题设置
            </Typography>
          </Box>

          {/* 返回按钮 */}
          <MenuItem
            onClick={() => setShowThemeMenu(false)}
            sx={{
              py: 1.5,
              '&:focus-visible': {
                outline: 'none',
              },
            }}
          >
            <KeyboardArrowLeft sx={{ mr: 1.5, fontSize: '1.25rem' }} />
            <Typography level="body-md">返回</Typography>
          </MenuItem>
          <Divider />

          {/* 主题选项 */}
          <MenuItem
            onClick={() => toggleColorMode('light')}
            sx={{
              py: 1.5,
              '&:focus-visible': {
                outline: 'none',
              },
            }}
          >
            <LightMode sx={{ mr: 1.5, fontSize: '1.25rem' }} />
            <Typography level="body-md">亮色模式</Typography>
            {mode === 'light' && <Chip size="sm" variant="soft" color="primary" sx={{ ml: 'auto' }}>当前</Chip>}
          </MenuItem>
          <MenuItem
            onClick={() => toggleColorMode('dark')}
            sx={{
              py: 1.5,
              '&:focus-visible': {
                outline: 'none',
              },
            }}
          >
            <DarkMode sx={{ mr: 1.5, fontSize: '1.25rem' }} />
            <Typography level="body-md">暗色模式</Typography>
            {mode === 'dark' && <Chip size="sm" variant="soft" color="primary" sx={{ ml: 'auto' }}>当前</Chip>}
          </MenuItem>
        </>
      );
    }

    return (
      <>
        {/* 顶部用户信息 */}
        <Box sx={{ px: 3, py: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Avatar size="md">
              <Person sx={{ fontSize: '1.25rem' }} />
            </Avatar>
            <Box>
              <Typography level="body-md" fontWeight="lg" sx={{ fontSize: '1rem' }}>
                {user?.name}
              </Typography>
              <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
                在线
              </Typography>
            </Box>
          </Stack>
        </Box>

        <MenuItem sx={{
          py: 1.5,
          '&:focus-visible': {
            outline: 'none',
          },
        }}>
          <Person sx={{ mr: 1.5, fontSize: '1.25rem' }} />
          <Typography level="body-md">账户管理</Typography>
        </MenuItem>

        {/* 设置菜单项 - 使用嵌套 Dropdown */}
        <Dropdown>
          <MenuButton
            slots={{ root: MenuItem }}
            slotProps={{
              root: {
                sx: {
                  py: 1.5,
                  '&:focus-visible': {
                    outline: 'none',
                  },
                }
              }
            }}
          >
            <Settings sx={{ mr: 1.5, fontSize: '1.25rem' }} />
            <Typography level="body-md">设置</Typography>
            <KeyboardArrowRight sx={{ ml: 'auto', fontSize: '1.25rem' }} />
          </MenuButton>
          <Menu placement="right-start" sx={{ minWidth: 200 }}>
            <MenuItem
              onClick={() => setShowThemeMenu(true)}
              sx={{
                py: 1.5,
                '&:focus-visible': {
                  outline: 'none',
                },
              }}
            >
              <LightMode sx={{ mr: 1.5, fontSize: '1.25rem' }} />
              <Typography level="body-md">主题设置</Typography>
            </MenuItem>
          </Menu>
        </Dropdown>

        <Divider />
        <MenuItem
          onClick={handleLogout}
          color="danger"
          sx={{
            py: 1.5,
            '&:focus-visible': {
              outline: 'none',
            },
          }}
        >
          <ExitToApp sx={{ mr: 1.5, fontSize: '1.25rem' }} />
          <Typography level="body-md">退出</Typography>
        </MenuItem>
      </>
    );
  }, [showThemeMenu, user?.name, mode, toggleColorMode, handleLogout]);

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sheet sx={sidebarStyles}>
        {/* 顶部区域 */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" spacing={1} justifyContent="space-between">
            {sidebarOpen && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <Box
                  component="img"
                  src="/logo.png"
                  alt="锤磨AI Logo"
                  sx={{ width: 32, height: 32 }}
                />
                <Typography level="title-lg" fontWeight="bold" sx={{ fontSize: '1.25rem' }}>
                  锤磨AI
                </Typography>
              </Box>
            )}
            <IconButton
              variant="plain"
              size="md"
              onClick={toggleSidebar}
              sx={{
                borderRadius: 'sm',
                '&:hover': {
                  backgroundColor: 'background.level2',
                },
                ...(sidebarOpen ? {} : {
                  p: 0.75,
                  minHeight: 40,
                  minWidth: 40,
                }),
              }}
            >
              {sidebarOpen ? (
                <KeyboardArrowLeft sx={{ fontSize: '1.25rem' }} />
              ) : (
                <Box
                  component="img"
                  src="/logo.png"
                  alt="锤磨AI Logo"
                  sx={{
                    width: 24,
                    height: 24,
                    display: 'block'
                  }}
                />
              )}
            </IconButton>
          </Stack>
        </Box>

        {/* 主导航菜单 */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <List sx={{ gap: 0.5, p: 2 }}>
            {navigationItems.map(item => renderNavItem(item))}
          </List>
        </Box>

        {/* 底部用户区域 */}
        <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          {sidebarOpen ? (
            /* 用户信息 */
            user && (
              <Dropdown>
                <MenuButton
                  variant="plain"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    width: '100%',
                    p: 1.5,
                    pl: 2, // 与菜单项对齐
                    borderRadius: 'md',
                    transition: 'background-color 0.15s ease-in-out',
                    '&:hover': {
                      backgroundColor: 'background.level2',
                    },
                    // 移除选中状态的边框
                    '&:focus-visible': {
                      outline: 'none',
                      backgroundColor: 'background.level2',
                    },
                  }}
                  onMouseEnter={() => setShowThemeMenu(false)} // 鼠标进入时重置状态
                >
                  <Avatar size="md" sx={{ width: 32, height: 32 }}>
                    <Person sx={{ fontSize: '1.25rem' }} />
                  </Avatar>
                  <Box sx={{ flex: 1, textAlign: 'left', ml: 1.5 }}>
                    <Typography level="body-md" fontWeight="md" sx={{ fontSize: '1rem' }}>
                      {user.name}
                    </Typography>
                  </Box>
                </MenuButton>
                <Menu placement="top-start" sx={{ minWidth: 240 }}>
                  {renderUserMenuContent()}
                </Menu>
              </Dropdown>
            )
          ) : (
            user && (
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Dropdown>
                  <MenuButton
                    variant="plain"
                    sx={{
                      p: 1,
                      borderRadius: 'md',
                      transition: 'background-color 0.15s ease-in-out',
                      '&:hover': {
                        backgroundColor: 'background.level2',
                      },
                      '&:focus-visible': {
                        outline: 'none',
                        backgroundColor: 'background.level2',
                      },
                    }}
                    onMouseEnter={() => setShowThemeMenu(false)} // 鼠标进入时重置状态
                  >
                    <Avatar size="md" sx={{ width: 32, height: 32 }}>
                      <Person sx={{ fontSize: '1.25rem' }} />
                    </Avatar>
                  </MenuButton>
                  <Menu placement="right-start" sx={{ minWidth: 280 }}>
                    {renderUserMenuContent()}
                  </Menu>
                </Dropdown>
              </Box>
            )
          )}
        </Box>
      </Sheet>

      {/* 主内容区域 */}
      <Box sx={mainContentStyles}>
        {/* 顶部导航栏 */}
        <Sheet
          sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
            p: 2,
            backgroundColor: 'background.surface',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography level="h4" fontWeight="md">
              锤磨AI助手
            </Typography>
            <Stack direction="row" spacing={1}>
              <Chip variant="soft" color="success">
                在线
              </Chip>
            </Stack>
          </Box>
        </Sheet>

        {/* 页面内容 */}
        <Box
          sx={{
            flex: 1,
            p: 3,
            backgroundColor: 'background.level1',
            overflow: 'auto',
            minHeight: 0,
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default AppLayout; 